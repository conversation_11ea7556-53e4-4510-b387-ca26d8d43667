[{"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/AboutSection.tsx": "1", "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/ContactSection.tsx": "2", "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/ExperienceSection.tsx": "3", "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HeroSection.tsx": "4", "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HobbiesSection.tsx": "5", "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/Navigation.tsx": "6", "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/TechStackSection.tsx": "7", "/Users/<USER>/Documents/working/frontend/resume-job/src/app/layout.tsx": "8", "/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx": "9"}, {"size": 2167, "mtime": 1753937392026, "results": "10", "hashOfConfig": "11"}, {"size": 3133, "mtime": 1753937402235, "results": "12", "hashOfConfig": "11"}, {"size": 2399, "mtime": 1753937297700, "results": "13", "hashOfConfig": "11"}, {"size": 2102, "mtime": 1753937413887, "results": "14", "hashOfConfig": "11"}, {"size": 2148, "mtime": 1753937322886, "results": "15", "hashOfConfig": "11"}, {"size": 2031, "mtime": 1753937261585, "results": "16", "hashOfConfig": "11"}, {"size": 2033, "mtime": 1753937311211, "results": "17", "hashOfConfig": "11"}, {"size": 949, "mtime": 1753936415121, "results": "18", "hashOfConfig": "11"}, {"size": 734, "mtime": 1753937242035, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "asc02d", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/AboutSection.tsx", [], [], "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/ContactSection.tsx", [], [], "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/ExperienceSection.tsx", [], [], "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HeroSection.tsx", [], [], "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HobbiesSection.tsx", [], [], "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/Navigation.tsx", [], [], "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/TechStackSection.tsx", [], [], "/Users/<USER>/Documents/working/frontend/resume-job/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx", [], []]