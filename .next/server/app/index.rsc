1:"$Sreact.fragment"
2:I[7555,[],""]
3:I[1295,[],""]
4:I[4366,["974","static/chunks/app/page-a0e159343fef5fff.js"],"default"]
5:I[961,["974","static/chunks/app/page-a0e159343fef5fff.js"],"default"]
10:I[8393,[],""]
:HL["/_next/static/media/569ce4b8f30dc480-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/93f479601ee12b01-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/css/a3c5671bfd5b5591.css","style"]
0:{"P":null,"b":"aviJvob6gS_-F6Y1oJ8K1","p":"","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/a3c5671bfd5b5591.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900","children":[["$","$L4",null,{}],["$","$L5",null,{}],["$","section",null,{"id":"about","className":"py-20 px-4 sm:px-6 lg:px-8","children":["$","div",null,{"className":"max-w-4xl mx-auto","children":[["$","h2",null,{"className":"text-4xl font-bold text-white mb-12 text-center","children":"About Me"}],["$","div",null,{"className":"grid md:grid-cols-2 gap-12 items-center","children":[["$","div",null,{"children":[["$","p",null,{"className":"text-lg text-gray-300 mb-6 leading-relaxed","children":"I'm a passionate frontend developer with a love for creating intuitive and beautiful user experiences. With expertise in modern web technologies, I enjoy turning complex problems into simple, elegant solutions."}],["$","p",null,{"className":"text-lg text-gray-300 mb-6 leading-relaxed","children":"When I'm not coding, you'll find me exploring new technologies, contributing to open-source projects, or pursuing my various hobbies that keep me inspired and creative."}],["$","div",null,{"className":"flex flex-wrap gap-4","children":[["$","div",null,{"className":"flex items-center text-purple-400","children":[["$","svg",null,{"className":"w-5 h-5 mr-2","fill":"currentColor","viewBox":"0 0 20 20","children":["$","path",null,{"fillRule":"evenodd","d":"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z","clipRule":"evenodd"}]}],"Based in Vietnam"]}],["$","div",null,{"className":"flex items-center text-purple-400","children":[["$","svg",null,{"className":"w-5 h-5 mr-2","fill":"currentColor","viewBox":"0 0 20 20","children":[["$","path",null,{"d":"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}],["$","path",null,{"d":"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"}]]}],"Open to opportunities"]}]]}]]}],["$","div",null,{"className":"relative","children":["$","div",null,{"className":"w-full h-80 bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center text-6xl font-bold text-white","children":"👨‍💻"}]}]]}]]}]}],["$","section",null,{"id":"experience","className":"py-20 px-4 sm:px-6 lg:px-8 bg-black/20","children":["$","div",null,{"className":"max-w-6xl mx-auto","children":[["$","h2",null,{"className":"text-4xl font-bold text-white mb-12 text-center","children":"Experience"}],["$","div",null,{"className":"space-y-8","children":[["$","div","0",{"className":"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10","children":["$L6","$L7","$L8"]}],"$L9","$La"]}]]}]}],"$Lb","$Lc","$Ld"]}],null,"$Le"]}],{},null,false]},null,false],"$Lf",false]],"m":"$undefined","G":["$10",[]],"s":false,"S":true}
18:I[9665,[],"OutletBoundary"]
1a:I[4911,[],"AsyncMetadataOutlet"]
1c:I[9665,[],"ViewportBoundary"]
1e:I[9665,[],"MetadataBoundary"]
1f:"$Sreact.suspense"
6:["$","div",null,{"className":"flex flex-col md:flex-row md:items-center md:justify-between mb-4","children":[["$","div",null,{"children":[["$","h3",null,{"className":"text-xl font-semibold text-white","children":"Senior Frontend Developer"}],["$","p",null,{"className":"text-purple-400 font-medium","children":"Tech Company"}]]}],["$","span",null,{"className":"text-gray-400 text-sm mt-2 md:mt-0","children":"2022 - Present"}]]}]
7:["$","p",null,{"className":"text-gray-300 mb-4","children":"Led frontend development for multiple web applications using React, Next.js, and TypeScript. Collaborated with design and backend teams to deliver high-quality user experiences."}]
8:["$","div",null,{"className":"flex flex-wrap gap-2","children":[["$","span","0",{"className":"px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm","children":"React"}],["$","span","1",{"className":"px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm","children":"Next.js"}],["$","span","2",{"className":"px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm","children":"TypeScript"}],["$","span","3",{"className":"px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm","children":"Tailwind CSS"}]]}]
9:["$","div","1",{"className":"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10","children":[["$","div",null,{"className":"flex flex-col md:flex-row md:items-center md:justify-between mb-4","children":[["$","div",null,{"children":[["$","h3",null,{"className":"text-xl font-semibold text-white","children":"Frontend Developer"}],["$","p",null,{"className":"text-purple-400 font-medium","children":"Digital Agency"}]]}],["$","span",null,{"className":"text-gray-400 text-sm mt-2 md:mt-0","children":"2020 - 2022"}]]}],["$","p",null,{"className":"text-gray-300 mb-4","children":"Developed responsive websites and web applications for various clients. Focused on performance optimization and accessibility standards."}],["$","div",null,{"className":"flex flex-wrap gap-2","children":[["$","span","0",{"className":"px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm","children":"JavaScript"}],["$","span","1",{"className":"px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm","children":"Vue.js"}],["$","span","2",{"className":"px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm","children":"SCSS"}],["$","span","3",{"className":"px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm","children":"Webpack"}]]}]]}]
a:["$","div","2",{"className":"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10","children":[["$","div",null,{"className":"flex flex-col md:flex-row md:items-center md:justify-between mb-4","children":[["$","div",null,{"children":[["$","h3",null,{"className":"text-xl font-semibold text-white","children":"Junior Web Developer"}],["$","p",null,{"className":"text-purple-400 font-medium","children":"Startup"}]]}],["$","span",null,{"className":"text-gray-400 text-sm mt-2 md:mt-0","children":"2019 - 2020"}]]}],["$","p",null,{"className":"text-gray-300 mb-4","children":"Started my journey in web development, working on various projects and learning modern development practices."}],["$","div",null,{"className":"flex flex-wrap gap-2","children":[["$","span","0",{"className":"px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm","children":"HTML"}],["$","span","1",{"className":"px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm","children":"CSS"}],["$","span","2",{"className":"px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm","children":"JavaScript"}],["$","span","3",{"className":"px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm","children":"jQuery"}]]}]]}]
b:["$","section",null,{"id":"techstack","className":"py-20 px-4 sm:px-6 lg:px-8","children":["$","div",null,{"className":"max-w-6xl mx-auto","children":[["$","h2",null,{"className":"text-4xl font-bold text-white mb-12 text-center","children":"Tech Stack"}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-3 gap-8","children":[["$","div","0",{"className":"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-300","children":[["$","div",null,{"className":"text-center mb-6","children":[["$","div",null,{"className":"text-4xl mb-4","children":"🎨"}],["$","h3",null,{"className":"text-xl font-semibold text-white","children":"Frontend"}]]}],["$","div",null,{"className":"space-y-3","children":[["$","div","0",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"React"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}],["$","div","1",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"Next.js"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}],["$","div","2",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"Vue.js"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}],["$","div","3",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"TypeScript"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}],["$","div","4",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"JavaScript"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}],["$","div","5",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"HTML5"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}],["$","div","6",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":["$L11","$L12"]}],"$L13","$L14"]}]]}],"$L15","$L16"]}]]}]}]
c:["$","section",null,{"id":"hobbies","className":"py-20 px-4 sm:px-6 lg:px-8 bg-black/20","children":["$","div",null,{"className":"max-w-6xl mx-auto","children":[["$","h2",null,{"className":"text-4xl font-bold text-white mb-12 text-center","children":"Hobbies & Interests"}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6","children":[["$","div","0",{"className":"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-300 hover:transform hover:scale-105","children":["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"text-3xl mb-4","children":"📸"}],["$","h3",null,{"className":"text-lg font-semibold text-white mb-3","children":"Photography"}],["$","p",null,{"className":"text-gray-300 text-sm","children":"Capturing moments and exploring creative perspectives through the lens."}]]}]}],["$","div","1",{"className":"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-300 hover:transform hover:scale-105","children":["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"text-3xl mb-4","children":"🎮"}],["$","h3",null,{"className":"text-lg font-semibold text-white mb-3","children":"Gaming"}],["$","p",null,{"className":"text-gray-300 text-sm","children":"Enjoying various video games and staying updated with the latest releases."}]]}]}],["$","div","2",{"className":"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-300 hover:transform hover:scale-105","children":["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"text-3xl mb-4","children":"📚"}],["$","h3",null,{"className":"text-lg font-semibold text-white mb-3","children":"Reading"}],["$","p",null,{"className":"text-gray-300 text-sm","children":"Reading tech blogs, books about development, and sci-fi novels."}]]}]}],["$","div","3",{"className":"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-300 hover:transform hover:scale-105","children":["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"text-3xl mb-4","children":"🎵"}],["$","h3",null,{"className":"text-lg font-semibold text-white mb-3","children":"Music"}],["$","p",null,{"className":"text-gray-300 text-sm","children":"Listening to various genres and occasionally playing guitar."}]]}]}],["$","div","4",{"className":"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-300 hover:transform hover:scale-105","children":["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"text-3xl mb-4","children":"✈️"}],["$","h3",null,{"className":"text-lg font-semibold text-white mb-3","children":"Travel"}],["$","p",null,{"className":"text-gray-300 text-sm","children":"Exploring new places and experiencing different cultures."}]]}]}],["$","div","5",{"className":"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-300 hover:transform hover:scale-105","children":["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"text-3xl mb-4","children":"👨‍🍳"}],["$","h3",null,{"className":"text-lg font-semibold text-white mb-3","children":"Cooking"}],["$","p",null,{"className":"text-gray-300 text-sm","children":"Experimenting with new recipes and cuisines from around the world."}]]}]}],["$","div","6",{"className":"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-300 hover:transform hover:scale-105","children":["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"text-3xl mb-4","children":"💪"}],["$","h3",null,{"className":"text-lg font-semibold text-white mb-3","children":"Fitness"}],["$","p",null,{"className":"text-gray-300 text-sm","children":"Staying active through gym workouts and outdoor activities."}]]}]}],"$L17"]}]]}]}]
d:["$","section",null,{"id":"contact","className":"py-20 px-4 sm:px-6 lg:px-8","children":["$","div",null,{"className":"max-w-4xl mx-auto text-center","children":[["$","h2",null,{"className":"text-4xl font-bold text-white mb-8","children":"Let's Connect"}],["$","p",null,{"className":"text-xl text-gray-300 mb-12","children":"I'm always interested in new opportunities and collaborations. Feel free to reach out!"}],["$","div",null,{"className":"flex flex-col sm:flex-row gap-6 justify-center items-center","children":[["$","a",null,{"href":"mailto:<EMAIL>","className":"inline-flex items-center px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium rounded-full hover:from-purple-700 hover:to-pink-700 transition-all duration-300 transform hover:scale-105","children":[["$","svg",null,{"className":"mr-2 h-5 w-5","fill":"currentColor","viewBox":"0 0 20 20","children":[["$","path",null,{"d":"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}],["$","path",null,{"d":"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"}]]}],"Send Email"]}],["$","div",null,{"className":"flex space-x-6","children":[["$","a",null,{"href":"https://www.linkedin.com/in/nguy%E1%BB%85n-nam-9343931b4/","className":"text-gray-400 hover:text-purple-400 transition-colors","children":[["$","span",null,{"className":"sr-only","children":"LinkedIn"}],["$","svg",null,{"className":"h-6 w-6","fill":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"d":"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"}]}]]}],["$","a",null,{"href":"https://github.com/namnguyen6595","className":"text-gray-400 hover:text-purple-400 transition-colors","children":[["$","span",null,{"className":"sr-only","children":"GitHub"}],["$","svg",null,{"className":"h-6 w-6","fill":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"d":"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"}]}]]}]]}]]}]]}]}]
e:["$","$L18",null,{"children":["$L19",["$","$L1a",null,{"promise":"$@1b"}]]}]
f:["$","$1","h",{"children":[null,[["$","$L1c",null,{"children":"$L1d"}],["$","meta",null,{"name":"next-size-adjust","content":""}]],["$","$L1e",null,{"children":["$","div",null,{"hidden":true,"children":["$","$1f",null,{"fallback":null,"children":"$L20"}]}]}]]}]
11:["$","span",null,{"className":"text-gray-300","children":"CSS3"}]
12:["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]
13:["$","div","7",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"Tailwind CSS"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}]
14:["$","div","8",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"SCSS"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}]
15:["$","div","1",{"className":"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-300","children":[["$","div",null,{"className":"text-center mb-6","children":[["$","div",null,{"className":"text-4xl mb-4","children":"🛠️"}],["$","h3",null,{"className":"text-xl font-semibold text-white","children":"Tools & Others"}]]}],["$","div",null,{"className":"space-y-3","children":[["$","div","0",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"Git"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}],["$","div","1",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"Webpack"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}],["$","div","2",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"Vite"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}],["$","div","3",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"npm/yarn"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}],["$","div","4",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"ESLint"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}],["$","div","5",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"Prettier"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}],["$","div","6",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"Figma"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],"$L21","$L22"]}]]}],"$L23"]}]]}]
16:["$","div","2",{"className":"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-300","children":[["$","div",null,{"className":"text-center mb-6","children":[["$","div",null,{"className":"text-4xl mb-4","children":"⚙️"}],["$","h3",null,{"className":"text-xl font-semibold text-white","children":"Backend & Database"}]]}],["$","div",null,{"className":"space-y-3","children":[["$","div","0",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"Node.js"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}],["$","div","1",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"Express"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}],["$","div","2",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"MongoDB"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}],["$","div","3",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"PostgreSQL"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}],["$","div","4",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"Firebase"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}],["$","div","5",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"Supabase"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}],["$","div","6",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"REST APIs"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],"$L24","$L25"]}]]}],"$L26"]}]]}]
17:["$","div","7",{"className":"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-300 hover:transform hover:scale-105","children":["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"text-3xl mb-4","children":"🧠"}],["$","h3",null,{"className":"text-lg font-semibold text-white mb-3","children":"Learning"}],["$","p",null,{"className":"text-gray-300 text-sm","children":"Continuously learning new technologies and improving my skills."}]]}]}]
21:["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}]
22:["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]
23:["$","div","7",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"Adobe XD"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}]
24:["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}]
25:["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]
26:["$","div","7",{"className":"flex items-center justify-between p-3 bg-white/5 rounded-lg","children":[["$","span",null,{"className":"text-gray-300","children":"GraphQL"}],["$","div",null,{"className":"flex space-x-1","children":[["$","div","0",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","1",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","2",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","3",{"className":"w-2 h-2 rounded-full bg-purple-400"}],["$","div","4",{"className":"w-2 h-2 rounded-full bg-gray-600"}]]}]]}]
1d:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
19:null
27:I[8175,[],"IconMark"]
1b:{"metadata":[["$","title","0",{"children":"Nam's Portfolio | Frontend Developer"}],["$","meta","1",{"name":"description","content":"Personal portfolio showcasing my experience, hobbies, and technical skills as a frontend developer"}],["$","meta","2",{"name":"author","content":"Nam"}],["$","meta","3",{"name":"keywords","content":"frontend developer,portfolio,web development,React,Next.js"}],["$","link","4",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L27","5",{}]],"error":null,"digest":"$undefined"}
20:"$1b:metadata"
