(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{961:(e,t,l)=>{"use strict";l.d(t,{default:()=>r});var s=l(5155);function r(){return(0,s.jsxs)("section",{id:"hero",className:"min-h-screen flex items-center justify-center relative",children:[(0,s.jsxs)("div",{className:"text-center z-10",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)("div",{className:"w-32 h-32 mx-auto mb-6 rounded-full bg-gradient-to-r from-purple-400 to-pink-400 flex items-center justify-center text-4xl font-bold text-white",children:"N"})}),(0,s.jsxs)("h1",{className:"text-5xl md:text-7xl font-bold text-white mb-6",children:["Hi, I'm ",(0,s.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400",children:"Nam"})]}),(0,s.jsx)("p",{className:"text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto",children:"Frontend Developer passionate about creating beautiful, functional web experiences"}),(0,s.jsxs)("button",{onClick:()=>(e=>{let t=document.getElementById(e);t&&t.scrollIntoView({behavior:"smooth"})})("about"),className:"inline-flex items-center px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium rounded-full hover:from-purple-700 hover:to-pink-700 transition-all duration-300 transform hover:scale-105",children:["Get to know me",(0,s.jsx)("svg",{className:"ml-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 14l-7 7m0 0l-7-7m7 7V3"})})]})]}),(0,s.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"}),(0,s.jsx)("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"})]})]})}},2296:(e,t,l)=>{Promise.resolve().then(l.bind(l,961)),Promise.resolve().then(l.bind(l,4366))},4366:(e,t,l)=>{"use strict";l.d(t,{default:()=>n});var s=l(5155),r=l(2115);function n(){let[e,t]=(0,r.useState)("hero");return(0,r.useEffect)(()=>{let e=()=>{let e=window.scrollY+200;for(let l of["hero","about","experience","techstack","hobbies","contact"]){let s=document.getElementById(l);if(s){let{offsetTop:r,offsetHeight:n}=s;if(e>=r&&e<r+n){t(l);break}}}};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,s.jsx)("nav",{className:"fixed top-0 w-full bg-black/20 backdrop-blur-md z-50 border-b border-white/10",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-white",children:"Nam"}),(0,s.jsx)("div",{className:"hidden md:flex space-x-8",children:["About","Experience","Tech Stack","Hobbies","Contact"].map(t=>(0,s.jsx)("button",{onClick:()=>(e=>{let t=document.getElementById(e);t&&t.scrollIntoView({behavior:"smooth"})})(t.toLowerCase().replace(" ","")),className:"text-sm font-medium transition-colors hover:text-purple-400 ".concat(e===t.toLowerCase().replace(" ","")?"text-purple-400":"text-gray-300"),children:t},t))})]})})})}}},e=>{e.O(0,[441,964,358],()=>e(e.s=2296)),_N_E=e.O()}]);