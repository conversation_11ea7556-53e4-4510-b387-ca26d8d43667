/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[11].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[11].use[5]!./src/app/components/AboutSection.scss ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.about-section {
  padding: 5rem 1rem;
}
@media (min-width: 640px) {
  .about-section {
    padding: 5rem 1.5rem;
  }
}
@media (min-width: 1024px) {
  .about-section {
    padding: 5rem 2rem;
  }
}
.about-section .about-container {
  max-width: 64rem;
  margin: 0 auto;
}
.about-section .about-title {
  font-size: 2.25rem;
  font-weight: bold;
  color: white;
  margin-bottom: 3rem;
  text-align: center;
}
.about-section .about-grid {
  display: grid;
  gap: 3rem;
  align-items: center;
}
@media (min-width: 768px) {
  .about-section .about-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
.about-section .about-content .about-text {
  font-size: 1.125rem;
  color: #d1d5db;
  margin-bottom: 1.5rem;
  line-height: 1.75;
}
.about-section .about-content .about-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}
.about-section .about-content .about-badges .badge {
  display: flex;
  align-items: center;
  color: #a855f7;
}
.about-section .about-content .about-badges .badge .badge-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.5rem;
}
.about-section .about-visual {
  position: relative;
}
.about-section .about-visual .visual-card {
  width: 100%;
  height: 20rem;
  background: linear-gradient(to bottom right, #9333ea, #db2777);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3.75rem;
  font-weight: bold;
  color: white;
}
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[7].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[7].use[5]!./src/app/components/HeroSection.module.scss ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.HeroSection_heroSection__5C2Pn {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.HeroSection_heroContent__QvDFY {
  text-align: center;
  z-index: 10;
}

.HeroSection_avatarContainer__m2qLk {
  margin-bottom: 2rem;
}
.HeroSection_avatarContainer__m2qLk .HeroSection_avatar__EKD5t {
  width: 8rem;
  height: 8rem;
  margin: 0 auto 1.5rem;
  border-radius: 50%;
  background: linear-gradient(to right, #a855f7, #ec4899);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.25rem;
  font-weight: bold;
  color: white;
}

.HeroSection_heroTitle__J_KM7 {
  font-size: 3rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1.5rem;
}
@media (min-width: 768px) {
  .HeroSection_heroTitle__J_KM7 {
    font-size: 4.5rem;
  }
}
.HeroSection_heroTitle__J_KM7 .HeroSection_nameHighlight__EkNTk {
  background: linear-gradient(to right, #a855f7, #ec4899);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.HeroSection_heroSubtitle__g6sCh {
  font-size: 1.25rem;
  color: #d1d5db;
  margin-bottom: 2rem;
  max-width: 42rem;
  margin-left: auto;
  margin-right: auto;
}
@media (min-width: 768px) {
  .HeroSection_heroSubtitle__g6sCh {
    font-size: 1.5rem;
  }
}

.HeroSection_ctaButton__FxEM2 {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 2rem;
  background: linear-gradient(to right, #9333ea, #db2777);
  color: white;
  font-weight: 500;
  border-radius: 9999px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.HeroSection_ctaButton__FxEM2:hover {
  transform: scale(1.05);
}
.HeroSection_ctaButton__FxEM2:hover {
  background: linear-gradient(to right, #7c3aed, #be185d);
}
.HeroSection_ctaButton__FxEM2 .HeroSection_ctaIcon__jv5De {
  margin-left: 0.5rem;
  height: 1.25rem;
  width: 1.25rem;
}

.HeroSection_backgroundEffects__Hd5vv {
  position: absolute;
  inset: 0;
  overflow: hidden;
}
.HeroSection_backgroundEffects__Hd5vv .HeroSection_floatingOrb__LKRlt {
  position: absolute;
  width: 20rem;
  height: 20rem;
  border-radius: 50%;
  mix-blend-mode: multiply;
  filter: blur(40px);
  opacity: 0.2;
  animation: HeroSection_pulse__C7s19 4s ease-in-out infinite;
}
.HeroSection_backgroundEffects__Hd5vv .HeroSection_floatingOrb__LKRlt.HeroSection_orb1__7m0Tp {
  top: -10rem;
  right: -10rem;
  background-color: #a855f7;
}
.HeroSection_backgroundEffects__Hd5vv .HeroSection_floatingOrb__LKRlt.HeroSection_orb2__pxV7B {
  bottom: -10rem;
  left: -10rem;
  background-color: #ec4899;
  animation-delay: 2s;
}

@keyframes HeroSection_pulse__C7s19 {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.4;
  }
}
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[7].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[7].use[5]!./src/app/components/Navigation.module.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.Navigation_navigation__gwt2t {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 50;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
}
.Navigation_navigation__gwt2t .Navigation_container__NGtU6 {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}
@media (min-width: 640px) {
  .Navigation_navigation__gwt2t .Navigation_container__NGtU6 {
    padding: 0 1.5rem;
  }
}
@media (min-width: 1024px) {
  .Navigation_navigation__gwt2t .Navigation_container__NGtU6 {
    padding: 0 2rem;
  }
}
.Navigation_navigation__gwt2t .Navigation_navContent__O_zJu {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}
.Navigation_navigation__gwt2t .Navigation_logo__wJWVc {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
}
.Navigation_navigation__gwt2t .Navigation_navLinks__aJwFk {
  display: none;
  gap: 2rem;
}
@media (min-width: 768px) {
  .Navigation_navigation__gwt2t .Navigation_navLinks__aJwFk {
    display: flex;
  }
}
.Navigation_navigation__gwt2t .Navigation_navButton__UI834 {
  font-size: 0.875rem;
  font-weight: 500;
  color: #d1d5db;
  background: none;
  border: none;
  cursor: pointer;
  transition: color 0.3s ease;
}
.Navigation_navigation__gwt2t .Navigation_navButton__UI834:hover {
  color: #a855f7;
}
.Navigation_navigation__gwt2t .Navigation_navButton__UI834.Navigation_active__tj0UR {
  color: #a855f7;
}
