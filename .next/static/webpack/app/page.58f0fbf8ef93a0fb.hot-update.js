"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Home() {\n    _s();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('hero');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const handleScroll = {\n                \"Home.useEffect.handleScroll\": ()=>{\n                    const sections = [\n                        'hero',\n                        'about',\n                        'experience',\n                        'techstack',\n                        'hobbies',\n                        'contact'\n                    ];\n                    const scrollPosition = window.scrollY + 200;\n                    for (const section of sections){\n                        const element = document.getElementById(section);\n                        if (element) {\n                            const { offsetTop, offsetHeight } = element;\n                            if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {\n                                setActiveSection(section);\n                                break;\n                            }\n                        }\n                    }\n                }\n            }[\"Home.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Home.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], []);\n    const scrollToSection = (sectionId)=>{\n        const element = document.getElementById(sectionId);\n        if (element) {\n            element.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 w-full bg-black/20 backdrop-blur-md z-50 border-b border-white/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"Nam\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex space-x-8\",\n                                children: [\n                                    'About',\n                                    'Experience',\n                                    'Tech Stack',\n                                    'Hobbies',\n                                    'Contact'\n                                ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToSection(item.toLowerCase().replace(' ', '')),\n                                        className: \"text-sm font-medium transition-colors hover:text-purple-400 \".concat(activeSection === item.toLowerCase().replace(' ', '') ? 'text-purple-400' : 'text-gray-300'),\n                                        children: item\n                                    }, item, false, {\n                                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"hero\",\n                className: \"min-h-screen flex items-center justify-center relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-32 h-32 mx-auto mb-6 rounded-full bg-gradient-to-r from-purple-400 to-pink-400 flex items-center justify-center text-4xl font-bold text-white\",\n                                    children: \"N\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-7xl font-bold text-white mb-6\",\n                                children: [\n                                    \"Hi, I'm \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400\",\n                                        children: \"Nam\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto\",\n                                children: \"Frontend Developer passionate about creating beautiful, functional web experiences\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>scrollToSection('about'),\n                                className: \"inline-flex items-center px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium rounded-full hover:from-purple-700 hover:to-pink-700 transition-all duration-300 transform hover:scale-105\",\n                                children: [\n                                    \"Get to know me\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"ml-2 h-5 w-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"about\",\n                className: \"py-20 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white mb-12 text-center\",\n                            children: \"About Me\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-12 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-gray-300 mb-6 leading-relaxed\",\n                                            children: \"I'm a passionate frontend developer with a love for creating intuitive and beautiful user experiences. With expertise in modern web technologies, I enjoy turning complex problems into simple, elegant solutions.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-gray-300 mb-6 leading-relaxed\",\n                                            children: \"When I'm not coding, you'll find me exploring new technologies, contributing to open-source projects, or pursuing my various hobbies that keep me inspired and creative.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-purple-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mr-2\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                                lineNumber: 111,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Based in Vietnam\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-purple-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mr-2\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                                    lineNumber: 117,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                                    lineNumber: 118,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                            lineNumber: 116,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Open to opportunities\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-80 bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center text-6xl font-bold text-white\",\n                                        children: \"\\uD83D\\uDC68‍\\uD83D\\uDCBB\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"experience\",\n                className: \"py-20 px-4 sm:px-6 lg:px-8 bg-black/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white mb-12 text-center\",\n                            children: \"Experience\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8\",\n                            children: [\n                                {\n                                    title: \"Senior Frontend Developer\",\n                                    company: \"Tech Company\",\n                                    period: \"2022 - Present\",\n                                    description: \"Led frontend development for multiple web applications using React, Next.js, and TypeScript. Collaborated with design and backend teams to deliver high-quality user experiences.\",\n                                    technologies: [\n                                        \"React\",\n                                        \"Next.js\",\n                                        \"TypeScript\",\n                                        \"Tailwind CSS\"\n                                    ]\n                                },\n                                {\n                                    title: \"Frontend Developer\",\n                                    company: \"Digital Agency\",\n                                    period: \"2020 - 2022\",\n                                    description: \"Developed responsive websites and web applications for various clients. Focused on performance optimization and accessibility standards.\",\n                                    technologies: [\n                                        \"JavaScript\",\n                                        \"Vue.js\",\n                                        \"SCSS\",\n                                        \"Webpack\"\n                                    ]\n                                },\n                                {\n                                    title: \"Junior Web Developer\",\n                                    company: \"Startup\",\n                                    period: \"2019 - 2020\",\n                                    description: \"Started my journey in web development, working on various projects and learning modern development practices.\",\n                                    technologies: [\n                                        \"HTML\",\n                                        \"CSS\",\n                                        \"JavaScript\",\n                                        \"jQuery\"\n                                    ]\n                                }\n                            ].map((job, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col md:flex-row md:items-center md:justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-white\",\n                                                            children: job.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-purple-400 font-medium\",\n                                                            children: job.company\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 text-sm mt-2 md:mt-0\",\n                                                    children: job.period\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 mb-4\",\n                                            children: job.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: job.technologies.map((tech, techIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm\",\n                                                    children: tech\n                                                }, techIndex, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"techstack\",\n                className: \"py-20 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white mb-12 text-center\",\n                            children: \"Tech Stack\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                {\n                                    category: \"Frontend\",\n                                    icon: \"🎨\",\n                                    technologies: [\n                                        \"React\",\n                                        \"Next.js\",\n                                        \"Vue.js\",\n                                        \"TypeScript\",\n                                        \"JavaScript\",\n                                        \"HTML5\",\n                                        \"CSS3\",\n                                        \"Tailwind CSS\",\n                                        \"SCSS\"\n                                    ]\n                                },\n                                {\n                                    category: \"Tools & Others\",\n                                    icon: \"🛠️\",\n                                    technologies: [\n                                        \"Git\",\n                                        \"Webpack\",\n                                        \"Vite\",\n                                        \"npm/yarn\",\n                                        \"ESLint\",\n                                        \"Prettier\",\n                                        \"Figma\",\n                                        \"Adobe XD\"\n                                    ]\n                                },\n                                {\n                                    category: \"Backend & Database\",\n                                    icon: \"⚙️\",\n                                    technologies: [\n                                        \"Node.js\",\n                                        \"Express\",\n                                        \"MongoDB\",\n                                        \"PostgreSQL\",\n                                        \"Firebase\",\n                                        \"Supabase\",\n                                        \"REST APIs\",\n                                        \"GraphQL\"\n                                    ]\n                                }\n                            ].map((stack, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl mb-4\",\n                                                    children: stack.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-white\",\n                                                    children: stack.category\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: stack.technologies.map((tech, techIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 bg-white/5 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-300\",\n                                                            children: tech\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-1\",\n                                                            children: [\n                                                                ...Array(5)\n                                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 rounded-full \".concat(i < 4 ? 'bg-purple-400' : 'bg-gray-600')\n                                                                }, i, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                                    lineNumber: 216,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, techIndex, true, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"hobbies\",\n                className: \"py-20 px-4 sm:px-6 lg:px-8 bg-black/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white mb-12 text-center\",\n                            children: \"Hobbies & Interests\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: [\n                                {\n                                    title: \"Photography\",\n                                    icon: \"📸\",\n                                    description: \"Capturing moments and exploring creative perspectives through the lens.\"\n                                },\n                                {\n                                    title: \"Gaming\",\n                                    icon: \"🎮\",\n                                    description: \"Enjoying various video games and staying updated with the latest releases.\"\n                                },\n                                {\n                                    title: \"Reading\",\n                                    icon: \"📚\",\n                                    description: \"Reading tech blogs, books about development, and sci-fi novels.\"\n                                },\n                                {\n                                    title: \"Music\",\n                                    icon: \"🎵\",\n                                    description: \"Listening to various genres and occasionally playing guitar.\"\n                                },\n                                {\n                                    title: \"Travel\",\n                                    icon: \"✈️\",\n                                    description: \"Exploring new places and experiencing different cultures.\"\n                                },\n                                {\n                                    title: \"Cooking\",\n                                    icon: \"👨‍🍳\",\n                                    description: \"Experimenting with new recipes and cuisines from around the world.\"\n                                },\n                                {\n                                    title: \"Fitness\",\n                                    icon: \"💪\",\n                                    description: \"Staying active through gym workouts and outdoor activities.\"\n                                },\n                                {\n                                    title: \"Learning\",\n                                    icon: \"🧠\",\n                                    description: \"Continuously learning new technologies and improving my skills.\"\n                                }\n                            ].map((hobby, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-300 hover:transform hover:scale-105\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl mb-4\",\n                                                children: hobby.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white mb-3\",\n                                                children: hobby.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 text-sm\",\n                                                children: hobby.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"contact\",\n                className: \"py-20 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white mb-8\",\n                            children: \"Let's Connect\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 mb-12\",\n                            children: \"I'm always interested in new opportunities and collaborations. Feel free to reach out!\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-6 justify-center items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"mailto:<EMAIL>\",\n                                    className: \"inline-flex items-center px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium rounded-full hover:from-purple-700 hover:to-pink-700 transition-all duration-300 transform hover:scale-105\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"mr-2 h-5 w-5\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Send Email\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-purple-400 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"LinkedIn\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-purple-400 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"GitHub\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-purple-400 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Twitter\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"4ZXzqJncAw/6y42xu/qTHhBEkpc=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});