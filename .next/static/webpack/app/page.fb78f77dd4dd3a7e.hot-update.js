"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Home() {\n    _s();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('hero');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const handleScroll = {\n                \"Home.useEffect.handleScroll\": ()=>{\n                    const sections = [\n                        'hero',\n                        'about',\n                        'experience',\n                        'techstack',\n                        'hobbies',\n                        'contact'\n                    ];\n                    const scrollPosition = window.scrollY + 200;\n                    for (const section of sections){\n                        const element = document.getElementById(section);\n                        if (element) {\n                            const { offsetTop, offsetHeight } = element;\n                            if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {\n                                setActiveSection(section);\n                                break;\n                            }\n                        }\n                    }\n                }\n            }[\"Home.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Home.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], []);\n    const scrollToSection = (sectionId)=>{\n        const element = document.getElementById(sectionId);\n        if (element) {\n            element.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 w-full bg-black/20 backdrop-blur-md z-50 border-b border-white/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: \"Nam\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex space-x-8\",\n                                children: [\n                                    'About',\n                                    'Experience',\n                                    'Tech Stack',\n                                    'Hobbies',\n                                    'Contact'\n                                ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToSection(item.toLowerCase().replace(' ', '')),\n                                        className: \"text-sm font-medium transition-colors hover:text-purple-400 \".concat(activeSection === item.toLowerCase().replace(' ', '') ? 'text-purple-400' : 'text-gray-300'),\n                                        children: item\n                                    }, item, false, {\n                                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"hero\",\n                className: \"min-h-screen flex items-center justify-center relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-32 h-32 mx-auto mb-6 rounded-full bg-gradient-to-r from-purple-400 to-pink-400 flex items-center justify-center text-4xl font-bold text-white\",\n                                    children: \"N\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-7xl font-bold text-white mb-6\",\n                                children: [\n                                    \"Hi, I'm \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400\",\n                                        children: \"Nam\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto\",\n                                children: \"Frontend Developer passionate about creating beautiful, functional web experiences\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>scrollToSection('about'),\n                                className: \"inline-flex items-center px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium rounded-full hover:from-purple-700 hover:to-pink-700 transition-all duration-300 transform hover:scale-105\",\n                                children: [\n                                    \"Get to know me\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"ml-2 h-5 w-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"about\",\n                className: \"py-20 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white mb-12 text-center\",\n                            children: \"About Me\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-12 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-gray-300 mb-6 leading-relaxed\",\n                                            children: \"I'm a passionate frontend developer with a love for creating intuitive and beautiful user experiences. With expertise in modern web technologies, I enjoy turning complex problems into simple, elegant solutions.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-gray-300 mb-6 leading-relaxed\",\n                                            children: \"When I'm not coding, you'll find me exploring new technologies, contributing to open-source projects, or pursuing my various hobbies that keep me inspired and creative.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-purple-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mr-2\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                                lineNumber: 111,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Based in Vietnam\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-purple-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mr-2\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                                    lineNumber: 117,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                                    lineNumber: 118,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                            lineNumber: 116,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Open to opportunities\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-80 bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center text-6xl font-bold text-white\",\n                                        children: \"\\uD83D\\uDC68‍\\uD83D\\uDCBB\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"experience\",\n                className: \"py-20 px-4 sm:px-6 lg:px-8 bg-black/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white mb-12 text-center\",\n                            children: \"Experience\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8\",\n                            children: [\n                                {\n                                    title: \"Senior Frontend Developer\",\n                                    company: \"Tech Company\",\n                                    period: \"2022 - Present\",\n                                    description: \"Led frontend development for multiple web applications using React, Next.js, and TypeScript. Collaborated with design and backend teams to deliver high-quality user experiences.\",\n                                    technologies: [\n                                        \"React\",\n                                        \"Next.js\",\n                                        \"TypeScript\",\n                                        \"Tailwind CSS\"\n                                    ]\n                                },\n                                {\n                                    title: \"Frontend Developer\",\n                                    company: \"Digital Agency\",\n                                    period: \"2020 - 2022\",\n                                    description: \"Developed responsive websites and web applications for various clients. Focused on performance optimization and accessibility standards.\",\n                                    technologies: [\n                                        \"JavaScript\",\n                                        \"Vue.js\",\n                                        \"SCSS\",\n                                        \"Webpack\"\n                                    ]\n                                },\n                                {\n                                    title: \"Junior Web Developer\",\n                                    company: \"Startup\",\n                                    period: \"2019 - 2020\",\n                                    description: \"Started my journey in web development, working on various projects and learning modern development practices.\",\n                                    technologies: [\n                                        \"HTML\",\n                                        \"CSS\",\n                                        \"JavaScript\",\n                                        \"jQuery\"\n                                    ]\n                                }\n                            ].map((job, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col md:flex-row md:items-center md:justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-white\",\n                                                            children: job.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-purple-400 font-medium\",\n                                                            children: job.company\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 text-sm mt-2 md:mt-0\",\n                                                    children: job.period\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 mb-4\",\n                                            children: job.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: job.technologies.map((tech, techIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm\",\n                                                    children: tech\n                                                }, techIndex, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"techstack\",\n                className: \"py-20 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white mb-12 text-center\",\n                            children: \"Tech Stack\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                {\n                                    category: \"Frontend\",\n                                    icon: \"🎨\",\n                                    technologies: [\n                                        \"React\",\n                                        \"Next.js\",\n                                        \"Vue.js\",\n                                        \"TypeScript\",\n                                        \"JavaScript\",\n                                        \"HTML5\",\n                                        \"CSS3\",\n                                        \"Tailwind CSS\",\n                                        \"SCSS\"\n                                    ]\n                                },\n                                {\n                                    category: \"Tools & Others\",\n                                    icon: \"🛠️\",\n                                    technologies: [\n                                        \"Git\",\n                                        \"Webpack\",\n                                        \"Vite\",\n                                        \"npm/yarn\",\n                                        \"ESLint\",\n                                        \"Prettier\",\n                                        \"Figma\",\n                                        \"Adobe XD\"\n                                    ]\n                                },\n                                {\n                                    category: \"Backend & Database\",\n                                    icon: \"⚙️\",\n                                    technologies: [\n                                        \"Node.js\",\n                                        \"Express\",\n                                        \"MongoDB\",\n                                        \"PostgreSQL\",\n                                        \"Firebase\",\n                                        \"Supabase\",\n                                        \"REST APIs\",\n                                        \"GraphQL\"\n                                    ]\n                                }\n                            ].map((stack, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl mb-4\",\n                                                    children: stack.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-white\",\n                                                    children: stack.category\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: stack.technologies.map((tech, techIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 bg-white/5 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-300\",\n                                                            children: tech\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-1\",\n                                                            children: [\n                                                                ...Array(5)\n                                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 rounded-full \".concat(i < 4 ? 'bg-purple-400' : 'bg-gray-600')\n                                                                }, i, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                                    lineNumber: 216,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, techIndex, true, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"hobbies\",\n                className: \"py-20 px-4 sm:px-6 lg:px-8 bg-black/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white mb-12 text-center\",\n                            children: \"Hobbies & Interests\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: [\n                                {\n                                    title: \"Photography\",\n                                    icon: \"📸\",\n                                    description: \"Capturing moments and exploring creative perspectives through the lens.\"\n                                },\n                                {\n                                    title: \"Gaming\",\n                                    icon: \"🎮\",\n                                    description: \"Enjoying various video games and staying updated with the latest releases.\"\n                                },\n                                {\n                                    title: \"Reading\",\n                                    icon: \"📚\",\n                                    description: \"Reading tech blogs, books about development, and sci-fi novels.\"\n                                },\n                                {\n                                    title: \"Music\",\n                                    icon: \"🎵\",\n                                    description: \"Listening to various genres and occasionally playing guitar.\"\n                                },\n                                {\n                                    title: \"Travel\",\n                                    icon: \"✈️\",\n                                    description: \"Exploring new places and experiencing different cultures.\"\n                                },\n                                {\n                                    title: \"Cooking\",\n                                    icon: \"👨‍🍳\",\n                                    description: \"Experimenting with new recipes and cuisines from around the world.\"\n                                },\n                                {\n                                    title: \"Fitness\",\n                                    icon: \"💪\",\n                                    description: \"Staying active through gym workouts and outdoor activities.\"\n                                },\n                                {\n                                    title: \"Learning\",\n                                    icon: \"🧠\",\n                                    description: \"Continuously learning new technologies and improving my skills.\"\n                                }\n                            ].map((hobby, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-300 hover:transform hover:scale-105\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl mb-4\",\n                                                children: hobby.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white mb-3\",\n                                                children: hobby.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 text-sm\",\n                                                children: hobby.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"contact\",\n                className: \"py-20 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white mb-8\",\n                            children: \"Let's Connect\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 mb-12\",\n                            children: \"I'm always interested in new opportunities and collaborations. Feel free to reach out!\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-6 justify-center items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"mailto:<EMAIL>\",\n                                    className: \"inline-flex items-center px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium rounded-full hover:from-purple-700 hover:to-pink-700 transition-all duration-300 transform hover:scale-105\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"mr-2 h-5 w-5\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Send Email\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-purple-400 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"LinkedIn\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://github.com/namnguyen6595\",\n                                            className: \"text-gray-400 hover:text-purple-400 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"GitHub\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-purple-400 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Twitter\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"4ZXzqJncAw/6y42xu/qTHhBEkpc=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});