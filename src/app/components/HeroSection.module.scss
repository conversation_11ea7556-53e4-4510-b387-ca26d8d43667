// HeroSection component SCSS
@import '../styles/variables';

.heroSection {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.heroContent {
  text-align: center;
  z-index: 10;
}

.avatarContainer {
  margin-bottom: 2rem;
  
  .avatar {
    width: 8rem;
    height: 8rem;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    background: linear-gradient(to right, $primary-purple, $primary-pink);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.25rem;
    font-weight: bold;
    color: white;
  }
}

.heroTitle {
  font-size: 3rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1.5rem;
  
  @media (min-width: 768px) {
    font-size: 4.5rem;
  }
  
  .nameHighlight {
    @include gradient-text;
  }
}

.heroSubtitle {
  font-size: 1.25rem;
  color: #d1d5db;
  margin-bottom: 2rem;
  max-width: 42rem;
  margin-left: auto;
  margin-right: auto;
  
  @media (min-width: 768px) {
    font-size: 1.5rem;
  }
}

.ctaButton {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 2rem;
  background: linear-gradient(to right, #9333ea, #db2777);
  color: white;
  font-weight: 500;
  border-radius: 9999px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  @include hover-scale;
  
  &:hover {
    background: linear-gradient(to right, #7c3aed, #be185d);
  }
  
  .ctaIcon {
    margin-left: 0.5rem;
    height: 1.25rem;
    width: 1.25rem;
  }
}

.backgroundEffects {
  position: absolute;
  inset: 0;
  overflow: hidden;
  
  .floatingOrb {
    position: absolute;
    width: 20rem;
    height: 20rem;
    border-radius: 50%;
    mix-blend-mode: multiply;
    filter: blur(40px);
    opacity: 0.2;
    animation: pulse 4s ease-in-out infinite;
    
    &.orb1 {
      top: -10rem;
      right: -10rem;
      background-color: $primary-purple;
    }
    
    &.orb2 {
      bottom: -10rem;
      left: -10rem;
      background-color: $primary-pink;
      animation-delay: 2s;
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.4;
  }
}
