'use client';
import styles from './HeroSection.module.scss';

export default function HeroSection() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="hero" className={styles.heroSection}>
      <div className={styles.heroContent}>
        <div className={styles.avatarContainer}>
          <div className={styles.avatar}>
            N
          </div>
        </div>
        <h1 className={styles.heroTitle}>
          Hi, I&apos;m <span className={styles.nameHighlight}>Nam</span>
        </h1>
        <p className={styles.heroSubtitle}>
          Frontend Developer passionate about creating beautiful, functional web experiences
        </p>
        <button
          onClick={() => scrollToSection('about')}
          className={styles.ctaButton}
        >
          Get to know me
          <svg className={styles.ctaIcon} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </button>
      </div>

      {/* Animated background elements */}
      <div className={styles.backgroundEffects}>
        <div className={`${styles.floatingOrb} ${styles.orb1}`}></div>
        <div className={`${styles.floatingOrb} ${styles.orb2}`}></div>
      </div>
    </section>
  );
}
