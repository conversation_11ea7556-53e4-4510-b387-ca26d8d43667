export default function HobbiesSection() {
  const hobbies = [
    {
      title: "Photography",
      icon: "📸",
      description: "Capturing moments and exploring creative perspectives through the lens."
    },
    {
      title: "Gaming",
      icon: "🎮",
      description: "Enjoying various video games and staying updated with the latest releases."
    },
    {
      title: "Reading",
      icon: "📚",
      description: "Reading tech blogs, books about development, and sci-fi novels."
    },
    {
      title: "Music",
      icon: "🎵",
      description: "Listening to various genres and occasionally playing guitar."
    },
    {
      title: "Travel",
      icon: "✈️",
      description: "Exploring new places and experiencing different cultures."
    },
    {
      title: "Cooking",
      icon: "👨‍🍳",
      description: "Experimenting with new recipes and cuisines from around the world."
    },
    {
      title: "Fitness",
      icon: "💪",
      description: "Staying active through gym workouts and outdoor activities."
    },
    {
      title: "Learning",
      icon: "🧠",
      description: "Continuously learning new technologies and improving my skills."
    }
  ];

  return (
    <section id="hobbies" className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
      <div className="max-w-6xl mx-auto">
        <h2 className="text-4xl font-bold text-white mb-12 text-center">Hobbies & Interests</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {hobbies.map((hobby, index) => (
            <div key={index} className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-300 hover:transform hover:scale-105">
              <div className="text-center">
                <div className="text-3xl mb-4">{hobby.icon}</div>
                <h3 className="text-lg font-semibold text-white mb-3">{hobby.title}</h3>
                <p className="text-gray-300 text-sm">{hobby.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
