// Navigation component SCSS
@import '../styles/variables';

.navigation {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 50;
  border-bottom: 1px solid $glass-border;
  @include glass-card;
  
  .container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1rem;
    
    @media (min-width: 640px) {
      padding: 0 1.5rem;
    }
    
    @media (min-width: 1024px) {
      padding: 0 2rem;
    }
  }
  
  .navContent {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
  }
  
  .logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
  }
  
  .navLinks {
    display: none;
    gap: 2rem;
    
    @media (min-width: 768px) {
      display: flex;
    }
  }
  
  .navButton {
    font-size: 0.875rem;
    font-weight: 500;
    color: #d1d5db;
    background: none;
    border: none;
    cursor: pointer;
    transition: color 0.3s ease;
    
    &:hover {
      color: $primary-purple;
    }
    
    &.active {
      color: $primary-purple;
    }
  }
}
