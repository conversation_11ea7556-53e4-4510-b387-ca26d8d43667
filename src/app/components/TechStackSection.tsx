export default function TechStackSection() {
  const techStacks = [
    {
      category: "Frontend",
      icon: "🎨",
      technologies: ["React", "Next.js", "Vue.js", "TypeScript", "JavaScript", "HTML5", "CSS3", "Tailwind CSS", "SCSS"]
    },
    {
      category: "Tools & Others",
      icon: "🛠️",
      technologies: ["Git", "Webpack", "Vite", "npm/yarn", "ESLint", "Prettier", "Figma", "Adobe XD"]
    },
    {
      category: "Backend & Database",
      icon: "⚙️",
      technologies: ["Node.js", "Express", "MongoDB", "PostgreSQL", "Firebase", "Supabase", "REST APIs", "GraphQL"]
    }
  ];

  return (
    <section id="techstack" className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        <h2 className="text-4xl font-bold text-white mb-12 text-center">Tech Stack</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {techStacks.map((stack, index) => (
            <div key={index} className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-300">
              <div className="text-center mb-6">
                <div className="text-4xl mb-4">{stack.icon}</div>
                <h3 className="text-xl font-semibold text-white">{stack.category}</h3>
              </div>
              <div className="space-y-3">
                {stack.technologies.map((tech, techIndex) => (
                  <div key={techIndex} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <span className="text-gray-300">{tech}</span>
                    <div className="flex space-x-1">
                      {[...Array(5)].map((_, i) => (
                        <div key={i} className={`w-2 h-2 rounded-full ${i < 4 ? 'bg-purple-400' : 'bg-gray-600'}`}></div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
